# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js
.next/
out/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ChromaDB
ragify-backend-chroma/
demo-rag-chroma/
chroma_db/

# Temporary files
*.tmp
*.temp
temp/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Model files (large files)
*.bin
*.safetensors
*.onnx

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# Coverage reports
htmlcov/
.coverage
.coverage.*
.cache
coverage.xml

# pytest
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
